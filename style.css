/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1000px;
    margin: 30px auto;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    overflow: hidden;
}

/* 头部样式 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #34495e;
    color: white;
    padding: 30px;
}

.header-text h1 {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.header-text h2 {
    font-size: 28px;
    margin-bottom: 15px;
}

.job-title {
    font-size: 18px;
    color: #ecf0f1;
}

.profile-pic {
    width: 120px;
    height: 120px;
    overflow: hidden;
    border-radius: 5px;
    border: 3px solid #fff;
}

.profile-pic img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 联系信息样式 */
.contact-info {
    display: flex;
    flex-wrap: wrap;
    background-color: #ecf0f1;
    padding: 15px 30px;
    border-bottom: 1px solid #ddd;
}

.info-item {
    display: flex;
    align-items: center;
    margin-right: 30px;
    margin-bottom: 5px;
}

.info-item i {
    margin-right: 10px;
    color: #34495e;
    font-size: 18px;
}

/* 部分样式 */
.resume-section {
    padding: 25px 30px;
    border-bottom: 1px solid #eee;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #3498db;
}

.section-header i {
    font-size: 24px;
    margin-right: 15px;
    color: #34495e;
}

.section-header h3 {
    font-size: 22px;
    color: #34495e;
}

/* 教育经历样式 */
.edu-item {
    display: flex;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.edu-date, .work-date {
    width: 180px;
    font-weight: bold;
    color: #3498db;
}

.edu-school, .work-company {
    width: 250px;
    font-weight: bold;
}

.edu-major, .work-title {
    flex: 1;
    text-align: right;
    color: #555;
}

.edu-details {
    margin-top: 10px;
    padding-left: 10px;
    border-left: 2px solid #eee;
}

/* 工作经历样式 */
.work-item {
    display: flex;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.work-description {
    margin-bottom: 30px;
    padding-left: 10px;
    border-left: 2px solid #eee;
}

.work-description ul {
    margin-top: 10px;
    margin-left: 20px;
}

.work-description li {
    margin-bottom: 8px;
}

/* 技能样式 */
.skills {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.skill-group h4 {
    color: #34495e;
    margin-bottom: 8px;
    font-size: 18px;
}

.skill-group p {
    margin-bottom: 5px;
}

/* 项目经验 */
.project-item {
    margin-bottom: 25px;
}

.project-item h4 {
    color: #34495e;
    font-size: 18px;
    margin-bottom: 8px;
}

.project-item ul {
    margin-top: 10px;
    margin-left: 20px;
}

.project-item li {
    margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
        border-radius: 0;
    }
    
    header {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-pic {
        margin-top: 20px;
    }
    
    .edu-item, .work-item {
        flex-direction: column;
    }
    
    .edu-major, .work-title {
        text-align: left;
        margin-top: 5px;
    }
    
    .skills {
        grid-template-columns: 1fr;
    }
} 