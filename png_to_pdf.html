<!DOCTYPE html>
<html>
<head>
    <title>PNG to PDF Converter</title>
    <meta charset="UTF-8">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: sans-serif;
            padding: 20px;
        }
        #output {
            margin-top: 15px;
            color: green;
        }
        #error {
            margin-top: 15px;
            color: red;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .info {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-left: 4px solid #3498db;
            margin: 15px 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        input[type="file"] {
            display: block;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PNG 转 PDF</h1>
        
        <div class="info">
            <p><strong>文件大小限制：</strong> 生成的PDF不会超过20MB。如果原始PNG超过大小限制，系统会自动压缩图像质量。</p>
        </div>

        <p>选择一个 PNG 文件:</p>
        <input type="file" id="pngFile" accept="image/png">

        <button onclick="convertToPdf()">转换为 PDF</button>

        <div id="output"></div>
        <div id="error"></div>
    </div>

    <script src="script.js"></script>
</body>
</html> 