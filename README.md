# 云原生全栈工程师简历模板

这是一个适合云原生全栈工程师求职的响应式简历模板，以突出Docker、Kubernetes等云原生技术相关的经验和技能。

## 特点

- 响应式设计，适配不同设备
- 专业的排版和样式
- 突出云原生技术栈和工作经验
- 支持一键打印功能
- 项目经验可折叠展示
- 技能卡片悬停效果

## 如何使用

1. 下载所有文件到本地文件夹
2. 使用文本编辑器（如VS Code、Notepad++等）打开`index.html`文件
3. 根据您的个人信息和经验修改HTML内容
4. 将您的个人照片替换`placeholder-profile.jpg`文件
5. 可以根据需要调整`style.css`中的样式
6. 在浏览器中打开`index.html`预览效果
7. 使用右下角的"打印简历"按钮生成PDF版本

## 文件结构

- `index.html`: 简历的HTML结构和内容
- `style.css`: 简历的样式表
- `script.js`: 简历的交互功能
- `placeholder-profile.jpg`: 个人照片占位图（请替换为您的照片）

## 自定义提示

- 个人信息部分：修改姓名、联系方式和求职意向
- 教育背景：修改学校、专业和主修课程
- 工作经历：添加或修改您的工作经验，重点突出与云原生技术相关的项目和职责
- 技能证书：根据您的实际情况调整技术技能和证书
- 项目经验：详细描述您参与的关键项目，特别是Docker、K8S等云原生技术的应用

## 注意事项

- 请确保所有信息真实准确
- 技能和经验要与求职岗位相匹配
- 简历内容应简明扼要，突出重点
- 可以根据不同公司的岗位要求微调简历内容 

## 更新日志

### 2024-11-05 更新
- 修复了跨域图片导致的PDF和图片导出失败问题
- 添加了图片预处理功能，自动将图片转换为Base64格式，解决画布污染问题
- 优化了图片加载流程，提高了导出成功率
- 添加了更详细的错误处理和提示信息 

### 2024-11-06 更新
- 解决了因上一版本修复导出问题时引入的`crossorigin="anonymous"`属性，导致本地图片无法显示的问题。
- 移除了HTML中`<img>`标签的`crossorigin`属性，确保图片在本地环境中（如使用`file:///`协议打开）能够正常显示。
- 调整了`html2canvas`库的配置，设置`allowTaint: false`，以防止在图片预处理失败时，因"画布污染"导致导出功能出错。
- 图片预处理功能 (`preprocessImages`) 仍然会尝试将图片转换为Base64格式以供导出，但现在即使预处理某些图片失败，页面上的图片显示也不会受影响。 

### 2024-11-07 更新
- 将网页中的蓝色元素（#4a90e2）更改为更深的商务蓝色（#2c5282）
- 调整了悬停状态的按钮颜色为更深的蓝色（#1a365d）
- 优化了整体视觉风格，使简历更具专业感和商务气质
- 统一了所有蓝色元素的色调，提升了设计的一致性 

### 2024-11-08 更新
- 全面修复了PDF和图片导出功能中的"画布污染"(tainted canvas)问题
- 优化了图片预处理逻辑，增加了多层错误处理和备选方案
- 添加了图片加载失败时自动创建占位图像的功能
- 改进了DOM克隆和处理方法，避免修改原始页面内容
- 优化了导出过程中的错误提示，显示具体错误信息而非通用提示
- 解决了在某些浏览器环境下无法成功导出PDF和图片的兼容性问题 

### 2024-11-09 更新
- 针对"下载图片"功能：修复了在生成的图片中，原简历内的图片有时不显示的问题。
- 确保了在截图前，所有图片（包括经过预处理转换的Base64图片或占位符）在临时渲染的DOM中都已完全加载。
- 优化了`preprocessImages`函数调用时机和目标DOM元素，确保其作用于即将被`html2canvas`处理的正确内容上。
- 加强了图片加载的等待逻辑，提高了图片在最终导出图像中的显示成功率。
- 完善了错误处理和临时元素的清理流程。 

### 2024-11-10 更新
- 移除了"下载简历 (图片)"功能中的图片预览步骤。
- 现在点击图片下载按钮后，将直接触发浏览器下载生成的图片文件，与PDF下载行为保持一致。
- 优化了下载流程中的状态提示和临时元素清理。 

### 2024-11-11 更新
- 为"下载简历 (图片)"功能增加了详细的诊断日志记录功能。
- 改进了`preprocessImages`函数，包括更准确的图片处理计数、对无`src`属性图片的特殊处理、以及使用`naturalWidth`/`naturalHeight`获取尺寸。
- 新增`setPlaceholderImage`辅助函数，用于在图片加载/转换失败时生成带错误信息的占位图。
- 在`html2canvas`执行前增加了对临时`div`中所有图片状态（src、complete、dimensions）的校验日志。
- 这些改动旨在帮助定位和解决在最终下载的图片中，简历内的图片不显示的问题。 

### 2024-11-12 更新
- 针对"下载简历 (图片)"功能，进一步增强了在图片加载检查阶段 (`Promise.all(tempImagePromises)`) 的日志记录。
- 为`tempImagePromises`中的每个Promise添加了详细的创建和解析（`onload`/`onerror`）日志，以追踪其状态。
- 明确区分了对已经是data URI的图片和非data URI图片的检查逻辑及相应日志。
- 优化了几个关键错误捕获点的日志和用户提示信息，使其更具区分度。
- 目的是精确诊断为何在`preprocessImages`执行后，`Promise.all(tempImagePromises)`可能未按预期解析，从而导致`html2canvas`调用失败或生成的图片缺少内容。 

### 2024-11-13 更新
- 完全重写了"下载简历 (图片)"功能，使用iframe作为隔离环境解决图片不显示问题。
- 采用了更可靠的图片加载方式，包括将相对路径转换为绝对路径，并显示加载进度。
- 复制原页面的样式表和字体到iframe中，确保内容正确显示。
- 为每张图片设置明确的宽度和高度，确保正确渲染。
- 增加了更健壮的错误处理和清晰的状态提示。
- 使用html2canvas直接对iframe的body进行截图，设置明确的窗口尺寸参数。
- 彻底解决了"画布污染"问题和图片路径解析问题，确保下载的图片中能正确显示所有图片。 