document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动效果
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // 为每个技能添加悬停效果
    const skillGroups = document.querySelectorAll('.skill-group');
    skillGroups.forEach(group => {
        group.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        group.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 添加打印功能
    const addPrintButton = () => {
        const container = document.querySelector('.container');
        const printBtn = document.createElement('button');
        printBtn.innerHTML = '打印简历';
        printBtn.className = 'print-btn';
        printBtn.style.position = 'fixed';
        printBtn.style.bottom = '20px';
        printBtn.style.right = '20px';
        printBtn.style.padding = '10px 15px';
        printBtn.style.backgroundColor = '#3498db';
        printBtn.style.color = 'white';
        printBtn.style.border = 'none';
        printBtn.style.borderRadius = '5px';
        printBtn.style.cursor = 'pointer';
        printBtn.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
        printBtn.style.zIndex = '1000';
        
        printBtn.addEventListener('click', () => {
            window.print();
        });
        
        // 添加悬停效果
        printBtn.addEventListener('mouseenter', () => {
            printBtn.style.backgroundColor = '#2980b9';
        });
        
        printBtn.addEventListener('mouseleave', () => {
            printBtn.style.backgroundColor = '#3498db';
        });
        
        document.body.appendChild(printBtn);
    };
    
    addPrintButton();

    // 添加项目经验展开/折叠功能
    const projectItems = document.querySelectorAll('.project-item');
    projectItems.forEach(item => {
        const heading = item.querySelector('h4');
        const details = item.querySelectorAll('p, ul');
        
        // 初始状态：显示
        let isExpanded = true;
        
        // 添加一个展开/折叠指示器
        heading.style.cursor = 'pointer';
        const indicator = document.createElement('span');
        indicator.innerHTML = ' [-]';
        indicator.style.fontSize = '16px';
        indicator.style.color = '#3498db';
        heading.appendChild(indicator);
        
        // 添加点击事件
        heading.addEventListener('click', () => {
            isExpanded = !isExpanded;
            
            details.forEach(el => {
                el.style.display = isExpanded ? 'block' : 'none';
            });
            
            indicator.innerHTML = isExpanded ? ' [-]' : ' [+]';
        });
    });

    // 为了打印样式，添加媒体查询样式
    const style = document.createElement('style');
    style.innerHTML = `
        @media print {
            body {
                background-color: white;
            }
            .container {
                box-shadow: none;
                margin: 0;
            }
            .print-btn {
                display: none;
            }
            @page {
                margin: 0.5cm;
            }
        }
    `;
    document.head.appendChild(style);
}); 

// 确保在使用 jsPDF 之前，它已经被正确加载
// 在上面的 HTML 中，我们通过 CDN 引入了它
const { jsPDF } = window.jspdf;

/**
 * 将图像压缩到指定的最大文件大小
 * @param {HTMLImageElement} img 原始图像元素
 * @param {number} maxSizeMB 最大文件大小（MB）
 * @return {Promise<string>} 返回压缩后的图像的 base64 数据
 */
function compressImage(img, maxSizeMB) {
    return new Promise((resolve, reject) => {
        // 创建一个 canvas 元素来绘制和压缩图像
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // 初始尺寸是原始图像的尺寸
        let width = img.naturalWidth;
        let height = img.naturalHeight;
        canvas.width = width;
        canvas.height = height;
        
        // 初始质量
        let quality = 1.0;
        const maxSizeBytes = maxSizeMB * 1024 * 1024; // 将 MB 转换为字节
        
        // 压缩函数
        const compress = () => {
            // 清除 canvas 并绘制图像
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0, width, height);
            
            // 将 canvas 转换为 base64 数据
            const dataUrl = canvas.toDataURL('image/jpeg', quality);
            
            // 估算数据大小（保守估计，base64 数据比实际文件大约大 1/3）
            const estimatedBytes = Math.ceil((dataUrl.length * 3) / 4);
            
            if (estimatedBytes > maxSizeBytes && quality > 0.1) {
                // 如果文件仍然太大且质量可以进一步降低
                quality -= 0.1;
                compress();
            } else if (estimatedBytes > maxSizeBytes && width > 1000) {
                // 如果降低质量不足以减小文件大小，则缩小图像尺寸
                width = Math.floor(width * 0.8);
                height = Math.floor(height * 0.8);
                canvas.width = width;
                canvas.height = height;
                quality = 0.8; // 重置质量
                compress();
            } else {
                // 文件大小已经足够小或者我们无法进一步压缩
                resolve(dataUrl);
            }
        };
        
        // 开始压缩
        compress();
    });
}

function convertToPdf() {
    const fileInput = document.getElementById('pngFile');
    const outputDiv = document.getElementById('output');
    const errorDiv = document.getElementById('error');
    const MAX_PDF_SIZE_MB = 20; // 最大 PDF 大小，单位为 MB

    outputDiv.textContent = ''; // 清除之前的输出
    errorDiv.textContent = ''; // 清除之前的错误

    if (fileInput.files.length === 0) {
        errorDiv.textContent = '请先选择一个 PNG 文件！';
        return;
    }

    const file = fileInput.files[0];

    if (file.type !== 'image/png') {
         errorDiv.textContent = '请选择 PNG 格式的图片文件。';
         return;
    }

    // 检查原始文件大小
    if (file.size > MAX_PDF_SIZE_MB * 1024 * 1024) {
        outputDiv.textContent = `原始文件大小为 ${(file.size / (1024 * 1024)).toFixed(2)} MB，超过 ${MAX_PDF_SIZE_MB} MB，将进行压缩...`;
    }

    const reader = new FileReader();

    reader.onload = function(event) {
        const imgData = event.target.result; // 这是 base64 编码的图像数据
        const img = new Image();

        img.onload = async function() {
            const imgWidth = img.naturalWidth;
            const imgHeight = img.naturalHeight;

            try {
                // 尝试压缩图像，使其总大小不超过限制
                const compressedImgData = await compressImage(img, MAX_PDF_SIZE_MB * 0.9); // 用 90% 的限制来预留一些空间
                
                // 使用图像的原始像素尺寸创建 PDF
                const orientation = imgWidth >= imgHeight ? 'l' : 'p';
                const pdf = new jsPDF({
                    orientation: orientation,
                    unit: 'px',
                    format: [imgWidth, imgHeight] // 直接使用图像宽高作为页面尺寸
                });

                // 将压缩后的图像添加到 PDF
                pdf.addImage(compressedImgData, 'JPEG', 0, 0, imgWidth, imgHeight);

                // 生成并下载 PDF 文件
                const filename = file.name.replace(/\.png$/i, '.pdf'); // 基于原文件名生成 PDF 文件名
                pdf.save(filename);

                outputDiv.textContent = `成功将 ${file.name} 转换为 ${filename}！确保大小不超过 ${MAX_PDF_SIZE_MB} MB`;
            } catch (error) {
                errorDiv.textContent = `处理图像时出错: ${error.message}`;
            }
        };

        img.onerror = function() {
            errorDiv.textContent = '无法加载图像数据。';
        };

        img.src = imgData; // 设置图像源，触发 img.onload
    };

    reader.onerror = function() {
        errorDiv.textContent = '读取文件时出错。';
    };

    reader.readAsDataURL(file); // 读取文件内容为 Data URL
} 